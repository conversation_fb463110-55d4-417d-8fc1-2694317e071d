import React, { useState, Ref, useEffect } from 'react';
import { Button, Tabs } from 'antd';
import { YTHForm, YTHPickUser } from 'yth-ui';
import type { Form } from '@formily/core/esm/models';
import { ActionType } from 'yth-ui/es/components/form/listFilter';
import { IYTHFormListColumnProps } from 'yth-ui/es/components/form/list';
import { CurrentUser } from '@/Constant';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import { Unit, User } from '@/service/system';
import baseApi from '@/service/baseApi';
import { IAction, IUserProps } from 'yth-ui/es/components/pickUser';
import style from './index.module.less';

const { TabPane } = Tabs;

// 定义学习对象类型
interface DicDataType {
  code: string;
  text: string;
}

// 扩展IUserProps接口，包含更多用户信息
interface ExtendedUserProps extends IUserProps {
  phone?: string;
  userCode?: string;
}

const defaultQueryData: { learningObject: DicDataType[] } = {
  learningObject: [
    { code: 'A08A39A01', text: '主要负责人' },
    { code: 'A08A39A02', text: '安全管理人员' },
    { code: 'A08A39A03', text: '员工' },
    { code: 'A08A39A04', text: '承包商' },
  ],
};

type PropsTypes = {
  /** 完成回调 */
  onComplete?: (data: Record<string, string>[]) => void;
  /** 关闭弹框方法 */
  closeModal?: () => void;
  /** 默认查询参数 */
  defaultQuery?: Record<string, unknown> & {
    learningObject?: DicDataType[];
  };
};

/**
 * @description 考核人员安排页面
 * @returns React.FC
 */
const AssessmentPersonnelList: React.FC<PropsTypes> = ({
  onComplete, // 完成回调
  closeModal, // 关闭弹框方法
  defaultQuery = {},
}) => {
  // 获取learningObject数据，优先使用传入的defaultQuery，否则使用默认数据
  const learningObjectData: DicDataType[] =
    defaultQuery?.learningObject || defaultQueryData.learningObject;

  // 当前激活的tab key - 直接初始化为第一个选项的code
  const [activeTabKey, setActiveTabKey] = useState<string>(
    learningObjectData && learningObjectData.length > 0 ? learningObjectData[0].code : '',
  );
  const form: Form = YTHForm.createForm({});
  const currentRef: Ref<
    ActionType & {
      addRows: (name: string, data: object[]) => void;
    }
  > = React.useRef();
  const PickRef: Ref<IAction> = React.useRef();

  // 确保activeTabKey有值（防御性编程）
  useEffect(() => {
    if (learningObjectData && learningObjectData.length > 0 && !activeTabKey) {
      setActiveTabKey(learningObjectData[0].code);
    }
  }, [learningObjectData, activeTabKey]);

  // tab切换处理函数
  const handleTabChange: (key: string) => void = (key: string) => {
    setActiveTabKey(key);
    // 可以在这里添加调试信息，查看当前表单数据
    console.log('切换到tab:', key, '当前表单数据:', form.values);
  };
  // 获取统一的列配置
  const getColumnsForTab: (tabItem: DicDataType) => IYTHFormListColumnProps[] = (
    tabItem: DicDataType,
  ) => {
    return [
      {
        title: '姓名',
        name: 'name',
        minWidth: 120,
        edit: true,
        componentName: 'Input' as const,
        componentProps: {},
        required: true,
      },
      {
        title: '对象类型',
        name: 'objectType',
        minWidth: 120,
        edit: false, // 不可编辑，自动填充
        componentName: 'Input' as const,
        componentProps: {
          disabled: true,
        },
        required: true,
        // 默认值设置为当前tab的名称
        defaultValue: tabItem.text,
      },
      {
        title: '工号',
        name: 'employeeId',
        minWidth: 120,
        edit: true,
        componentName: 'Input' as const,
        componentProps: {},
        required: true,
      },
      {
        title: '联系电话',
        name: 'phone',
        minWidth: 150,
        edit: true,
        componentName: 'Input' as const,
        componentProps: {},
        required: true,
      },
    ];
  };

  // 渲染tab内容
  const renderTabContent: (tabItem: DicDataType) => React.ReactNode = (tabItem) => {
    return (
      <YTHForm.List
        columns={getColumnsForTab(tabItem)}
        name={`${tabItem.code}List`}
        rowOperations={[]}
        extra={[
          {
            key: 'addPersonnel',
            title: '安排',
            type: 'main',
            disabled: false,
            operation: async () => {
              PickRef?.current.click();
            },
          },
        ]}
        title=""
        actionRef={currentRef}
      />
    );
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <div>
        <Tabs activeKey={activeTabKey} onChange={handleTabChange}>
          {learningObjectData.map((item: DicDataType) => (
            <TabPane
              tab={
                <span>
                  {item.text}
                  {/* 显示当前tab的数据数量 */}
                  {form.values[`${item.code}List`] &&
                    form.values[`${item.code}List`].length > 0 && (
                      <span style={{ marginLeft: 4, color: '#1890ff' }}>
                        ({form.values[`${item.code}List`].length})
                      </span>
                    )}
                </span>
              }
              key={item.code}
            >
              {/* Tab内容现在只显示当前激活的tab，但所有表单数据都保存在同一个form中 */}
            </TabPane>
          ))}
        </Tabs>

        {/* 只渲染当前激活tab的表单内容，数据通过form实例持久化 */}
        <YTHForm form={form}>
          {activeTabKey && learningObjectData.find((item) => item.code === activeTabKey) &&
            renderTabContent(learningObjectData.find((item) => item.code === activeTabKey)!)
          }
        </YTHForm>
      </div>
      <div className={style['pick-user-container']}>
        <YTHPickUser
          defaultOrganize={{
            id: CurrentUser()?.accountId,
            name: '',
            type: 'org',
          }}
          actionRef={PickRef}
          requestOrganize={async () => {
            const resData: Unit = await baseApi.getUnitTree();
            return formatTree(resData, 'unitType', 'unitName');
          }}
          searchMode="1"
          multiple
          onChange={(users: ExtendedUserProps[]) => {
            console.log('选中的用户:', users);

            // 根据当前激活的tab获取对应的YTHForm.List的name
            const currentTabData: DicDataType | undefined = learningObjectData.find(
              (item) => item.code === activeTabKey,
            );
            if (!currentTabData) {
              return;
            }

            const listName: string = `${currentTabData.code}List`;

            // 将选中的用户数据转换为表单需要的格式
            const formattedUsers: Array<{
              name: string;
              objectType: string;
              employeeId: string;
              phone: string;
            }> = users.map((user: ExtendedUserProps) => ({
              name: user.name, // 用户姓名
              objectType: currentTabData.text, // 对象类型，根据当前tab设置
              employeeId: user.userCode || user.id, // 优先使用工号，否则使用用户ID
              phone: user.phone || '', // 使用用户的联系电话，如果没有则为空
            }));

            // 添加数据到对应的列表中
            currentRef?.current?.addRows(listName, formattedUsers);
          }}
          remoteSearch
          requestUser={async (organize) => {
            const resData: User[] = await baseApi.getUserList(organize.id);
            const newData: ExtendedUserProps[] = [];
            resData.forEach((item: User) => {
              newData.push({
                id: item.id,
                name: item.realName,
                type: 'user',
                // 扩展用户信息，便于后续表单数据映射
                phone: item.phone,
                userCode: item.userCode,
              });
            });
            return newData;
          }}
        />
      </div>

      <div className={style['btn-container']}>
        <Button
          onClick={() => {
            closeModal?.();
          }}
        >
          关闭
        </Button>
        <Button
          type="primary"
          style={{ marginLeft: '10px' }}
          onClick={() => {
            console.log('form.values', form.values);
            onComplete?.(form.values);
            closeModal?.();
          }}
        >
          保存
        </Button>
      </div>
    </div>
  );
};

export default AssessmentPersonnelList;
